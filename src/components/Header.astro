---
import { Icon } from '@lucide/astro';
---

<header x-data x-init="$store.i18n.init()" class="navbar bg-base-100 shadow-md">
  <div class="navbar-start">
    <a class="btn btn-ghost text-xl" href="/" x-text="$store.i18n.t('footer.company')"></a>
  </div>
  <div class="navbar-center hidden lg:flex">
    <ul class="menu menu-horizontal px-1">
      <li><a href="#features" x-text="$store.i18n.t('nav.features')"></a></li>
      <li><a href="#contact" x-text="$store.i18n.t('nav.contact')"></a></li>
    </ul>
  </div>
  <div class="navbar-end">
    <!-- Theme Toggle -->
    <label class="swap swap-rotate btn btn-ghost">
      <input type="checkbox" @click="toggleTheme" :checked="theme === 'dark'" />
      <Icon name="sun" class="swap-on h-5 w-5" />
      <Icon name="moon" class="swap-off h-5 w-5" />
    </label>
    <!-- Language Switcher -->
    <div class="dropdown dropdown-end">
      <div tabindex="0" role="button" class="btn btn-ghost">
        <Icon name="languages" class="h-5 w-5" />
        <span x-text="$store.i18n.lang.toUpperCase()"></span>
      </div>
      <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-200 rounded-box w-24">
        <li><a @click="$store.i18n.lang = 'en'">English</a></li>
        <li><a @click="$store.i18n.lang = 'zh'">中文</a></li>
      </ul>
    </div>
  </div>
</header>

