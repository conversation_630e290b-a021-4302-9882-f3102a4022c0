---
import { Icon } from '@lucide/astro';
---

<div id="features" class="container mx-auto px-4 py-16">
  <h2 class="text-4xl font-bold text-center mb-12" x-text="$store.i18n.t('features.title')"></h2>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
    <!-- Feature 1 -->
    <div class="card bg-base-100 shadow-xl text-center">
      <figure class="px-10 pt-10">
        <Icon name="rocket" class="w-20 h-20 text-primary" />
      </figure>
      <div class="card-body items-center text-center">
        <h3 class="card-title" x-text="$store.i18n.t('features.one.title')"></h3>
        <p x-text="$store.i18n.t('features.one.description')"></p>
      </div>
    </div>
    <!-- Feature 2 -->
    <div class="card bg-base-100 shadow-xl text-center">
      <figure class="px-10 pt-10">
        <Icon name="heart" class="w-20 h-20 text-secondary" />
      </figure>
      <div class="card-body items-center text-center">
        <h3 class="card-title" x-text="$store.i18n.t('features.two.title')"></h3>
        <p x-text="$store.i18n.t('features.two.description')"></p>
      </div>
    </div>
    <!-- Feature 3 -->
    <div class="card bg-base-100 shadow-xl text-center">
      <figure class="px-10 pt-10">
        <Icon name="zap" class="w-20 h-20 text-accent" />
      </figure>
      <div class="card-body items-center text-center">
        <h3 class="card-title" x-text="$store.i18n.t('features.three.title')"></h3>
        <p x-text="$store.i18n.t('features.three.description')"></p>
      </div>
    </div>
  </div>
</div>
