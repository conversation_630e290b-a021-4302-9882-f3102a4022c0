import Alpine from 'alpinejs';

// Declare <PERSON> on window for TypeScript
declare global {
  interface Window {
    Alpine: typeof Alpine;
  }
}

// Theme management
Alpine.data('themeManager', () => ({
  theme: localStorage.getItem('theme') || 'light',
  init() {
    this.$watch('theme', (val: string) => {
      localStorage.setItem('theme', val);
      document.documentElement.setAttribute('data-theme', val);
    });
    document.documentElement.setAttribute('data-theme', this.theme);
  },
  toggleTheme() {
    this.theme = this.theme === 'light' ? 'dark' : 'light';
  }
}));

// i18n management
Alpine.store('i18n', {
  lang: 'en',
  data: {} as Record<string, any>,
  async init() {
    const savedLang = localStorage.getItem('lang');
    const browserLang = navigator.language.split('-')[0];
    this.lang = savedLang || (['en', 'zh'].includes(browserLang) ? browserLang : 'en');

    // Watch for language changes
    Alpine.effect(() => {
      if (this.lang) {
        localStorage.setItem('lang', this.lang);
        this.load();
      }
    });

    await this.load();
  },
  async load() {
    try {
      const response = await fetch(`/i18n/${this.lang}.json`);
      this.data = await response.json();
    } catch (error) {
      console.error('Failed to load language file:', error);
      this.data = {};
    }
  },
  t(key: string): string {
    return key.split('.').reduce((o: any, i: string) => (o && o[i] !== undefined) ? o[i] : key, this.data);
  }
});

window.Alpine = Alpine;
Alpine.start();
