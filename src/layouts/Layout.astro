---
import { ViewTransitions } from 'astro:transitions';

export interface Props {
	title: string;
}

const { title } = Astro.props;
---

<!doctype html>
<html lang="en" data-theme="light">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content="L-S Software" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
		<ViewTransitions />
    <!-- Alpine.js -->
    <script>
      import Alpine from 'alpinejs';
      window.Alpine = Alpine;
      Alpine.start();
    </script>
		<script is:inline>
			document.addEventListener('alpine:init', () => {
				// Theme management
				Alpine.data('themeManager', () => ({
					theme: localStorage.getItem('theme') || 'light',
					init() {
						this.$watch('theme', val => {
							localStorage.setItem('theme', val);
							document.documentElement.setAttribute('data-theme', val);
						});
						document.documentElement.setAttribute('data-theme', this.theme);
					},
					toggleTheme() {
						this.theme = this.theme === 'light' ? 'dark' : 'light';
					}
				}));

				// i18n management
				Alpine.store('i18n', {
					lang: 'en',
					data: {},
					async init() {
						const savedLang = localStorage.getItem('lang');
						const browserLang = navigator.language.split('-')[0];
						this.lang = savedLang || (['en', 'zh'].includes(browserLang) ? browserLang : 'en');

						this.$watch('lang', async val => {
							localStorage.setItem('lang', val);
							await this.load();
						});

						await this.load();
					},
					async load() {
						const response = await fetch(`/i18n/${this.lang}.json`);
						this.data = await response.json();
					},
					t(key) {
						return key.split('.').reduce((o, i) => (o ? o[i] : key), this.data);
					}
				});
			});
		</script>
	</head>
	<body x-data="themeManager" class="min-h-screen bg-base-100">
		<slot />
	</body>
</html>
